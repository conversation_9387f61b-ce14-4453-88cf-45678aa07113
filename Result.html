<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title></title>
<style type="text/css">
.ansi2html-content { display: inline; white-space: pre-wrap; word-wrap: break-word; }
.body_foreground { color: #AAAAAA; }
.body_background { background-color: #000000; }
.inv_foreground { color: #000000; }
.inv_background { background-color: #AAAAAA; }
.ansi1 { font-weight: bold; }
.ansi31 { color: #aa0000; }
</style>
</head>
<body class="body_foreground body_background" style="font-size: normal;" >
<pre class="ansi2html-content">
Script started on 2025-07-05 06:54:22+03:00 [COMMAND="swipl -x /home/<USER>/experments/metta-wam/prolog/metta_lang/Sav.obeds-hp-laptop-14-dq4xxx.MeTTaLog  -- --python=enable -- --html utilities/tests/lazy-random-testold.metta  " TERM="xterm-256color" TTY="/dev/pts/0" COLUMNS="98" LINES="17"]

[on_mettalog_error [error [error [missing_exception [load_metta &top utilities/tests/lazy-random-testold.metta]] $3129566]]]


[error [error [missing_exception [load_metta &top utilities/tests/lazy-random-testold.metta]] $3129566]]


[on_mettalog_error [error [give_up [error [error [missing_exception [load_metta &top utilities/tests/lazy-random-testold.metta]] $3132868]]]]]


[error [give_up [error [error [missing_exception [load_metta &top utilities/tests/lazy-random-testold.metta]] $3132868]]]]

<span class="ansi1 ansi31">ERROR: Unhandled exception: Unknown message: give_up(error(give_up(error(error(missing_exception(load_metta('&top','utilities/tests/lazy-random-testold.metta')),_3133258)))))
</span>
Script done on 2025-07-05 06:54:23+03:00 [COMMAND_EXIT_CODE="0"]

</pre>
</body>

</html>
