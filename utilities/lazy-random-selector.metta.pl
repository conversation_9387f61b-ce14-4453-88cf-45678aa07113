%% Generated from /home/<USER>/experments/metta-moses/utilities/lazy-random-selector.metta at 2025-07-05T07:43:47+03:00
:- style_check(-discontiguous).
:- style_check(-singleton).
:- include(library(metta_lang/metta_transpiled_header)).

%  ;; pick -> Pick a random number which is not in the list
%  ;; params:
%  ;;        $lower: lower bound of the random number
%  ;;        $upper: upper bound of the random number
%  ;;        $picked: list of picked numbers
%  ;; lazyRandomSelector -> Select $n random index of a list without replacement
%  ;; params:
%  ;;        $lower: lower bound of the random number
%  ;;        $upper: upper bound of the random number
%  ;;        $n: number of elements to select
%  ;;        $picked: list of picked numbers
%  ;;
%  ;; Return a list of selected indexs as Expression 
%  ;; (: lazyRandomSelector (-> Number Number Number Expression ))
%% Finished generating /home/<USER>/experments/metta-moses/utilities/lazy-random-selector.metta at 2025-07-05T07:43:47+03:00

:- normal_IO.
:- initialization(transpiled_main, program).
