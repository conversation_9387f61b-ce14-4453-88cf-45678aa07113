<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title></title>
<style type="text/css">
.ansi2html-content { display: inline; white-space: pre-wrap; word-wrap: break-word; }
.body_foreground { color: #AAAAAA; }
.body_background { background-color: #000000; }
.inv_foreground { color: #000000; }
.inv_background { background-color: #AAAAAA; }
.ansi1 { font-weight: bold; }
.ansi32 { color: #00aa00; }
.ansi33 { color: #aa5500; }
.ansi36 { color: #00aaaa; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
.ansi38-013099040 { color: #0D6328; }
</style>
</head>
<body class="body_foreground body_background" style="font-size: normal;" >
<pre class="ansi2html-content">
Script started on 2025-07-05 07:43:46+03:00 [COMMAND="swipl -x /home/<USER>/experments/metta-wam/prolog/metta_lang/Sav.obeds-hp-laptop-14-dq4xxx.MeTTaLog  -- --python=enable -- --html utilities/tests/lazy-random-test.metta  --stdout=pipe --output-filename=pipe:[85308] --stderr=pipe --error-filename=pipe:[84255]" TERM="xterm-256color" TTY="/dev/pts/0" COLUMNS="-1" LINES="-1"]
<span class="ansi38-013099040">!(register-module! ../../../metta-moses)

</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi38-013099040">!(import! &self metta-moses:utilities:lazy-random-selector)

</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi38-013099040">!(import! &self metta-moses:utilities:general-helpers)

</span><span class="ansi38-013099040">!(bind! EPSILON (pow-math 10 -6))

</span>
Deterministic: <span class="ansi33">()
</span>
R(2): <span class="ansi33">()
</span><span class="ansi38-013099040">!(assertEqual (length (lazyRandomSelector 0 5 3)) 3)

</span>
;<h3 id="TESTS.LAZY-RANDOM-TEST.01">;; TESTS.LAZY-RANDOM-TEST.01</h3>
; 
; EVAL TEST
; took 0.106 secs. (106.17 milliseconds) 

!(assertEqual (length (lazyRandomSelector 0 5 3)) 3)

<span class="ansi36">  [loonit_success 
    [equal_enough_for_test_renumbered_l strict_equals_allow_vn 
      (3) 
      (3)]]
</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi38-013099040">!(assertEqual (length (lazyRandomSelector 0 3 4)) 4)

</span>
;<h3 id="TESTS.LAZY-RANDOM-TEST.02">;; TESTS.LAZY-RANDOM-TEST.02</h3>
; 
; EVAL TEST
; took 0.056 secs. (56.42 milliseconds) 

!(assertEqual (length (lazyRandomSelector 0 3 4)) 4)

<span class="ansi36">  [loonit_success 
    [equal_enough_for_test_renumbered_l strict_equals_allow_vn 
      (4) 
      (4)]]
</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi38-013099040">!(assertEqual (length (lazyRandomSelector 0 5 5)) 5)

</span>
;<h3 id="TESTS.LAZY-RANDOM-TEST.03">;; TESTS.LAZY-RANDOM-TEST.03</h3>
; 
; EVAL TEST
; took 0.072 secs. (72.47 milliseconds) 

!(assertEqual (length (lazyRandomSelector 0 5 5)) 5)

<span class="ansi36">  [loonit_success 
    [equal_enough_for_test_renumbered_l strict_equals_allow_vn 
      (5) 
      (5)]]
</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi38-013099040">!(assertEqual (lazyRandomSelector 0 1 6) (Error 6 "number of elements to select is greater than list size"))

</span>
;<h3 id="TESTS.LAZY-RANDOM-TEST.04">;; TESTS.LAZY-RANDOM-TEST.04</h3>
; 
; EVAL TEST
; took 0.002 secs. (2.39 milliseconds) 

!(assertEqual (lazyRandomSelector 0 1 6) (Error 6 "number of elements to select is greater than list size"))

<span class="ansi36">  [loonit_success 
    [equal_enough_for_test_renumbered_l strict_equals_allow_vn 
      ( (Error 6 "number of elements to select is greater than list size")) 
      ( (Error 6 "number of elements to select is greater than list size"))]]
</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi38-013099040">!(assertEqual (length (lazyRandomSelector 0 6 0)) 0)

</span>
;<h3 id="TESTS.LAZY-RANDOM-TEST.05">;; TESTS.LAZY-RANDOM-TEST.05</h3>
; 
; EVAL TEST
; took 0.005 secs. (4.75 milliseconds) 

!(assertEqual (length (lazyRandomSelector 0 6 0)) 0)

<span class="ansi36">  [loonit_success 
    [equal_enough_for_test_renumbered_l strict_equals_allow_vn 
      (0) 
      (0)]]
</span>
Deterministic: <span class="ansi33">()
</span><span class="ansi1">LoonIt Report
</span>------------
<span class="ansi32">Successes: 5
</span><span class="ansi32">Failures: 0
</span>[()]
[()]
[()]
[()]
[()]
[()]
[()]
[()]

Script done on 2025-07-05 07:43:49+03:00 [COMMAND_EXIT_CODE="1"]

</pre>
</body>

</html>
