%% Generated from /home/<USER>/experments/metta-moses/utilities/tests/lazy-random-test.metta at 2025-07-05T07:43:47+03:00
:- style_check(-discontiguous).
:- style_check(-singleton).
:- include(library(metta_lang/metta_transpiled_header)).



<span class="pl-atom">top_call_5</span>:- <span class="pl-compound pl-level-0"><span class="pl-functor">do_metta_runtime</span>(<span class="pl-args"><span class="pl-var">ExecRes</span>, <span class="pl-compound"><span class="pl-functor">mi</span>(<span class="pl-args"><span class="pl-atom">'register-module!'</span>, <span class="pl-atom">'../../../metta-moses'</span>, <span class="pl-var">ExecRes</span></span>)</span></span>)</span>.




top_call :-
    time(top_call_5).




<span class="pl-atom">top_call_6</span>:- <span class="pl-compound pl-level-0"><span class="pl-functor">eval_H</span>(<span class="pl-args"><span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">'import!'</span>|<span class="pl-ellipsis">...</span></span>]</span>, <span class="pl-var">ExecRes</span></span>)</span>.




top_call :-
    time(top_call_6).




<span class="pl-atom">top_call_7</span>:- <span class="pl-compound pl-level-0"><span class="pl-functor">eval_H</span>(<span class="pl-args"><span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">'import!'</span>|<span class="pl-ellipsis">...</span></span>]</span>, <span class="pl-var">ExecRes</span></span>)</span>.




top_call :-
    time(top_call_7).


%  ;;Test case for lazy-random selector


<span class="pl-atom">top_call_9</span>:- <span class="pl-atom">do_metta_runtime</span><span class="pl-functor">( <span class="pl-var">ExecRes</span>, </span>
                                              ((&#13;&#10; 
                                               (<span class="pl-var">_Src_1</span> =  
                                                     
                                                  [ <span class="pl-atom">assertEqual</span>, 
                                                    [ <span class="pl-atom">length</span>, 
                                                      <span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>|<span class="pl-ellipsis">...</span></span>]</span>], 
                                                    <span class="pl-int">3</span>]),
                                               (<span class="pl-atom">loonit_assert_source_tf_empty</span>( <span class="pl-var">_Src_1</span>, 
                                                  <span class="pl-var">_FARL_1</span>, 
                                                  <span class="pl-var">_FARL_2</span>, 
                                                    ((&#13;&#10; 
                                                     (<span class="pl-atom">findall_ne</span>( <span class="pl-var">_FA_1</span>, 
                                                        ( <span class="pl-compound pl-level-0"><span class="pl-var">A</span><span class="pl-infix">=</span><span class="pl-atom">length</span></span>  ,
                                                          <span class="pl-compound pl-level-0"><span class="pl-functor">mi</span>(<span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>, <span class="pl-int">5</span>, <span class="pl-int">3</span>, <span class="pl-var">B</span></span>)</span> , 
                                                          <span class="pl-compound pl-level-0"><span class="pl-var">B</span><span class="pl-infix">=</span><span class="pl-var">C</span></span> , 
                                                          <span class="pl-compound pl-level-0"><span class="pl-functor">u_assign</span>(<span class="pl-args"><span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-var">A</span>|<span class="pl-ellipsis">...</span></span>]</span>, <span class="pl-var">_FA_1</span></span>)</span>), 
                                                        <span class="pl-var">_FARL_1</span>)),
                                                     (<span class="pl-compound pl-level-0"><span class="pl-functor">findall_ne</span>(<span class="pl-args"><span class="pl-int">3</span>, <span class="pl-atom">true</span>, <span class="pl-var">_FARL_2</span></span>)</span>)  )), 
                                                  <span class="pl-compound pl-level-0"><span class="pl-functor">equal_enough_for_test</span>(<span class="pl-args"><span class="pl-var">_FARL_1</span>, <span class="pl-var">_FARL_2</span></span>)</span>, 
                                                  <span class="pl-var">ExecRes</span>))  ))).




top_call :-
    time(top_call_9).


arg_type_n(==,2,1,var).
arg_type_n(==,2,2,var).
arg_type_n(pick,3,3,non_eval('Expression')).
arg_type_n(isMember,2,1,var).
arg_type_n(isMember,2,2,var).
arg_type_n(foldr,3,2,var).
arg_type_n(foldr,3,3,var).


<span class="pl-atom">top_call_10</span>:- <span class="pl-atom">do_metta_runtime</span><span class="pl-functor">( <span class="pl-var">ExecRes</span>, </span>
                                               ((&#13;&#10; 
                                                (<span class="pl-var">_Src_2</span> =  
                                                      
                                                   [ <span class="pl-atom">assertEqual</span>, 
                                                     [ <span class="pl-atom">length</span>, 
                                                       <span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>|<span class="pl-ellipsis">...</span></span>]</span>], 
                                                     <span class="pl-int">4</span>]),
                                                (<span class="pl-atom">loonit_assert_source_tf_empty</span>( <span class="pl-var">_Src_2</span>, 
                                                   <span class="pl-var">_FARL_3</span>, 
                                                   <span class="pl-var">_FARL_4</span>, 
                                                     ((&#13;&#10; 
                                                      (<span class="pl-atom">findall_ne</span>( <span class="pl-var">_FA_3</span>, 
                                                         ( <span class="pl-compound pl-level-0"><span class="pl-var">A</span><span class="pl-infix">=</span><span class="pl-atom">length</span></span>  ,
                                                           <span class="pl-compound pl-level-0"><span class="pl-functor">mi</span>(<span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>, <span class="pl-int">3</span>, <span class="pl-int">4</span>, <span class="pl-var">B</span></span>)</span> , 
                                                           <span class="pl-compound pl-level-0"><span class="pl-var">B</span><span class="pl-infix">=</span><span class="pl-var">C</span></span> , 
                                                           <span class="pl-compound pl-level-0"><span class="pl-functor">u_assign</span>(<span class="pl-args"><span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-var">A</span>|<span class="pl-ellipsis">...</span></span>]</span>, <span class="pl-var">_FA_3</span></span>)</span>), 
                                                         <span class="pl-var">_FARL_3</span>)),
                                                      (<span class="pl-compound pl-level-0"><span class="pl-functor">findall_ne</span>(<span class="pl-args"><span class="pl-int">4</span>, <span class="pl-atom">true</span>, <span class="pl-var">_FARL_4</span></span>)</span>)  )), 
                                                   <span class="pl-compound pl-level-0"><span class="pl-functor">equal_enough_for_test</span>(<span class="pl-args"><span class="pl-var">_FARL_3</span>, <span class="pl-var">_FARL_4</span></span>)</span>, 
                                                   <span class="pl-var">ExecRes</span>))  ))).




top_call :-
    time(top_call_10).




<span class="pl-atom">top_call_11</span>:- <span class="pl-atom">do_metta_runtime</span><span class="pl-functor">( <span class="pl-var">ExecRes</span>, </span>
                                               ((&#13;&#10; 
                                                (<span class="pl-var">_Src_3</span> =  
                                                      
                                                   [ <span class="pl-atom">assertEqual</span>, 
                                                     [ <span class="pl-atom">length</span>, 
                                                       <span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>|<span class="pl-ellipsis">...</span></span>]</span>], 
                                                     <span class="pl-int">5</span>]),
                                                (<span class="pl-atom">loonit_assert_source_tf_empty</span>( <span class="pl-var">_Src_3</span>, 
                                                   <span class="pl-var">_FARL_5</span>, 
                                                   <span class="pl-var">_FARL_6</span>, 
                                                     ((&#13;&#10; 
                                                      (<span class="pl-atom">findall_ne</span>( <span class="pl-var">_FA_5</span>, 
                                                         ( <span class="pl-compound pl-level-0"><span class="pl-var">A</span><span class="pl-infix">=</span><span class="pl-atom">length</span></span>  ,
                                                           <span class="pl-compound pl-level-0"><span class="pl-functor">mi</span>(<span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>, <span class="pl-int">5</span>, <span class="pl-int">5</span>, <span class="pl-var">B</span></span>)</span> , 
                                                           <span class="pl-compound pl-level-0"><span class="pl-var">B</span><span class="pl-infix">=</span><span class="pl-var">C</span></span> , 
                                                           <span class="pl-compound pl-level-0"><span class="pl-functor">u_assign</span>(<span class="pl-args"><span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-var">A</span>|<span class="pl-ellipsis">...</span></span>]</span>, <span class="pl-var">_FA_5</span></span>)</span>), 
                                                         <span class="pl-var">_FARL_5</span>)),
                                                      (<span class="pl-compound pl-level-0"><span class="pl-functor">findall_ne</span>(<span class="pl-args"><span class="pl-int">5</span>, <span class="pl-atom">true</span>, <span class="pl-var">_FARL_6</span></span>)</span>)  )), 
                                                   <span class="pl-compound pl-level-0"><span class="pl-functor">equal_enough_for_test</span>(<span class="pl-args"><span class="pl-var">_FARL_5</span>, <span class="pl-var">_FARL_6</span></span>)</span>, 
                                                   <span class="pl-var">ExecRes</span>))  ))).




top_call :-
    time(top_call_11).




<span class="pl-atom">top_call_12</span>:- <span class="pl-atom">do_metta_runtime</span><span class="pl-functor">( <span class="pl-var">ExecRes</span>, </span>
                                               ((&#13;&#10; 
                                                (<span class="pl-var">_Src_4</span> =  
                                                      
                                                   [ <span class="pl-atom">assertEqual</span>, 
                                                     <span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>|<span class="pl-ellipsis">...</span></span>]</span>, 
                                                     [ <span class="pl-atom">'Error'</span>, <span class="pl-int">6</span>, <span class="pl-string">"number of elements to select is greater than list size"</span>]]),
                                                (<span class="pl-atom">loonit_assert_source_tf_empty</span>( <span class="pl-var">_Src_4</span>, 
                                                   <span class="pl-var">_FARL_7</span>, 
                                                   <span class="pl-var">_FARL_8</span>, 
                                                     ((&#13;&#10; 
                                                      (<span class="pl-compound pl-level-0"><span class="pl-functor">findall_ne</span>(<span class="pl-args"><span class="pl-var">_FA_7</span>, <span class="pl-compound"><span class="pl-functor">mi</span>(<span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>, <span class="pl-int">1</span>, <span class="pl-int">6</span>, <span class="pl-var">_FA_7</span></span>)</span>, <span class="pl-var">_FARL_7</span></span>)</span>),
                                                      (<span class="pl-atom">findall_ne</span>( <span class="pl-var">_FA_8</span>, 
                                                         <span class="pl-var">_FA_8</span> =  
                                                              
                                                           [ <span class="pl-atom">'Error'</span>, <span class="pl-int">6</span>, <span class="pl-string">"number of elements to select is greater than list size"</span>], 
                                                         <span class="pl-var">_FARL_8</span>))  )), 
                                                   <span class="pl-compound pl-level-0"><span class="pl-functor">equal_enough_for_test</span>(<span class="pl-args"><span class="pl-var">_FARL_7</span>, <span class="pl-var">_FARL_8</span></span>)</span>, 
                                                   <span class="pl-var">ExecRes</span>))  ))).




top_call :-
    time(top_call_12).


%  ; ! (assertEqual (lazyRandomSelector 0 20 50) (Error 50 "number of elements to select is greater than list size"))


<span class="pl-atom">top_call_13</span>:- <span class="pl-atom">do_metta_runtime</span><span class="pl-functor">( <span class="pl-var">ExecRes</span>, </span>
                                               ((&#13;&#10; 
                                                (<span class="pl-var">_Src_5</span> =  
                                                      
                                                   [ <span class="pl-atom">assertEqual</span>, 
                                                     [ <span class="pl-atom">length</span>, 
                                                       <span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>|<span class="pl-ellipsis">...</span></span>]</span>], 
                                                     <span class="pl-int">0</span>]),
                                                (<span class="pl-atom">loonit_assert_source_tf_empty</span>( <span class="pl-var">_Src_5</span>, 
                                                   <span class="pl-var">_FARL_9</span>, 
                                                   <span class="pl-var">_FARL_10</span>, 
                                                     ((&#13;&#10; 
                                                      (<span class="pl-atom">findall_ne</span>( <span class="pl-var">_FA_9</span>, 
                                                         ( <span class="pl-compound pl-level-0"><span class="pl-var">A</span><span class="pl-infix">=</span><span class="pl-atom">length</span></span>  ,
                                                           <span class="pl-compound pl-level-0"><span class="pl-functor">mi</span>(<span class="pl-args"><span class="pl-atom">lazyRandomSelector</span>, <span class="pl-int">0</span>, <span class="pl-int">6</span>, <span class="pl-int">0</span>, <span class="pl-var">B</span></span>)</span> , 
                                                           <span class="pl-compound pl-level-0"><span class="pl-var">B</span><span class="pl-infix">=</span><span class="pl-var">C</span></span> , 
                                                           <span class="pl-compound pl-level-0"><span class="pl-functor">u_assign</span>(<span class="pl-args"><span class="pl-list"><span class="pl-functor"> [ </span><span class="pl-args"><span class="pl-var">A</span>|<span class="pl-ellipsis">...</span></span>]</span>, <span class="pl-var">_FA_9</span></span>)</span>), 
                                                         <span class="pl-var">_FARL_9</span>)),
                                                      (<span class="pl-compound pl-level-0"><span class="pl-functor">findall_ne</span>(<span class="pl-args"><span class="pl-int">0</span>, <span class="pl-atom">true</span>, <span class="pl-var">_FARL_10</span></span>)</span>)  )), 
                                                   <span class="pl-compound pl-level-0"><span class="pl-functor">equal_enough_for_test</span>(<span class="pl-args"><span class="pl-var">_FARL_9</span>, <span class="pl-var">_FARL_10</span></span>)</span>, 
                                                   <span class="pl-var">ExecRes</span>))  ))).




top_call :-
    time(top_call_13).


%% Finished generating /home/<USER>/experments/metta-moses/utilities/tests/lazy-random-test.metta at 2025-07-05T07:43:49+03:00

:- normal_IO.
:- initialization(transpiled_main, program).
